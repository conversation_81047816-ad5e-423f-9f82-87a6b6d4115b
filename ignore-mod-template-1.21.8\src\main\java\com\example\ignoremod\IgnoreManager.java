package com.example.ignoremod;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import net.fabricmc.loader.api.FabricLoader;

import java.io.*;
import java.lang.reflect.Type;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashSet;
import java.util.Set;

public class IgnoreManager {
    private static final String IGNORE_FILE = "ignored_players.json";
    private static final Gson GSON = new Gson();
    private static IgnoreManager instance;
    
    private final Set<String> ignoredPlayers;
    private final Path configDir;
    private final Path ignoreFile;
    
    private IgnoreManager() {
        this.ignoredPlayers = new HashSet<>();
        this.configDir = FabricLoader.getInstance().getConfigDir().resolve("ignore-mod");
        this.ignoreFile = configDir.resolve(IGNORE_FILE);
        
        // Створюємо директорію конфігурації якщо вона не існує
        try {
            Files.createDirectories(configDir);
        } catch (IOException e) {
            IgnoreMod.LOGGER.error("Failed to create config directory", e);
        }
    }
    
    public static IgnoreManager getInstance() {
        if (instance == null) {
            instance = new IgnoreManager();
        }
        return instance;
    }
    
    /**
     * Додає гравця до списку ігнорування
     */
    public boolean addIgnoredPlayer(String playerName) {
        if (playerName == null || playerName.trim().isEmpty()) {
            return false;
        }
        
        String normalizedName = playerName.toLowerCase().trim();
        boolean added = ignoredPlayers.add(normalizedName);
        
        if (added) {
            saveToFile();
            IgnoreMod.LOGGER.info("Added player '{}' to ignore list", normalizedName);
        }
        
        return added;
    }
    
    /**
     * Видаляє гравця зі списку ігнорування
     */
    public boolean removeIgnoredPlayer(String playerName) {
        if (playerName == null || playerName.trim().isEmpty()) {
            return false;
        }
        
        String normalizedName = playerName.toLowerCase().trim();
        boolean removed = ignoredPlayers.remove(normalizedName);
        
        if (removed) {
            saveToFile();
            IgnoreMod.LOGGER.info("Removed player '{}' from ignore list", normalizedName);
        }
        
        return removed;
    }
    
    /**
     * Перевіряє чи гравець знаходиться в списку ігнорування
     */
    public boolean isPlayerIgnored(String playerName) {
        if (playerName == null || playerName.trim().isEmpty()) {
            return false;
        }
        
        String normalizedName = playerName.toLowerCase().trim();
        return ignoredPlayers.contains(normalizedName);
    }
    
    /**
     * Отримує копію списку ігнорованих гравців
     */
    public Set<String> getIgnoredPlayers() {
        return new HashSet<>(ignoredPlayers);
    }
    
    /**
     * Очищає список ігнорованих гравців
     */
    public void clearIgnoredPlayers() {
        ignoredPlayers.clear();
        saveToFile();
        IgnoreMod.LOGGER.info("Cleared ignore list");
    }
    
    /**
     * Завантажує список ігнорованих гравців з файлу
     */
    public void loadFromFile() {
        if (!Files.exists(ignoreFile)) {
            IgnoreMod.LOGGER.info("Ignore file doesn't exist, starting with empty list");
            return;
        }
        
        try (Reader reader = Files.newBufferedReader(ignoreFile)) {
            Type setType = new TypeToken<Set<String>>(){}.getType();
            Set<String> loadedPlayers = GSON.fromJson(reader, setType);
            
            if (loadedPlayers != null) {
                ignoredPlayers.clear();
                ignoredPlayers.addAll(loadedPlayers);
                IgnoreMod.LOGGER.info("Loaded {} ignored players from file", ignoredPlayers.size());
            }
        } catch (IOException e) {
            IgnoreMod.LOGGER.error("Failed to load ignore list from file", e);
        }
    }
    
    /**
     * Зберігає список ігнорованих гравців у файл
     */
    private void saveToFile() {
        try (Writer writer = Files.newBufferedWriter(ignoreFile)) {
            GSON.toJson(ignoredPlayers, writer);
        } catch (IOException e) {
            IgnoreMod.LOGGER.error("Failed to save ignore list to file", e);
        }
    }
}

package com.example.ignoremod;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import net.fabricmc.fabric.api.client.command.v2.ClientCommandManager;
import net.fabricmc.fabric.api.client.command.v2.FabricClientCommandSource;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.Set;

public class IgnoreCommand {
    
    public static void register(CommandDispatcher<FabricClientCommandSource> dispatcher) {
        dispatcher.register(ClientCommandManager.literal("ignore")
            .then(ClientCommandManager.argument("player", StringArgumentType.word())
                .executes(IgnoreCommand::toggleIgnorePlayer))
            .then(ClientCommandManager.literal("list")
                .executes(IgnoreCommand::listIgnoredPlayers))
            .then(ClientCommandManager.literal("clear")
                .executes(IgnoreCommand::clearIgnoredPlayers))
            .executes(IgnoreCommand::showHelp));
    }
    
    private static int toggleIgnorePlayer(CommandContext<FabricClientCommandSource> context) {
        String playerName = StringArgumentType.getString(context, "player");
        IgnoreManager manager = IgnoreManager.getInstance();
        
        if (manager.isPlayerIgnored(playerName)) {
            // Видаляємо з списку ігнорування
            if (manager.removeIgnoredPlayer(playerName)) {
                context.getSource().sendFeedback(Text.literal("Гравець ")
                    .append(Text.literal(playerName).formatted(Formatting.YELLOW))
                    .append(Text.literal(" видалений зі списку ігнорування"))
                    .formatted(Formatting.GREEN));
            } else {
                context.getSource().sendError(Text.literal("Помилка при видаленні гравця зі списку ігнорування"));
            }
        } else {
            // Додаємо до списку ігнорування
            if (manager.addIgnoredPlayer(playerName)) {
                context.getSource().sendFeedback(Text.literal("Гравець ")
                    .append(Text.literal(playerName).formatted(Formatting.YELLOW))
                    .append(Text.literal(" доданий до списку ігнорування"))
                    .formatted(Formatting.GREEN));
            } else {
                context.getSource().sendError(Text.literal("Помилка при додаванні гравця до списку ігнорування"));
            }
        }
        
        return 1;
    }
    
    private static int listIgnoredPlayers(CommandContext<FabricClientCommandSource> context) {
        IgnoreManager manager = IgnoreManager.getInstance();
        Set<String> ignoredPlayers = manager.getIgnoredPlayers();
        
        if (ignoredPlayers.isEmpty()) {
            context.getSource().sendFeedback(Text.literal("Список ігнорованих гравців порожній")
                .formatted(Formatting.YELLOW));
        } else {
            context.getSource().sendFeedback(Text.literal("Ігноровані гравці (" + ignoredPlayers.size() + "):")
                .formatted(Formatting.GREEN));
            
            for (String player : ignoredPlayers) {
                context.getSource().sendFeedback(Text.literal("  - ")
                    .append(Text.literal(player).formatted(Formatting.YELLOW)));
            }
        }
        
        return 1;
    }
    
    private static int clearIgnoredPlayers(CommandContext<FabricClientCommandSource> context) {
        IgnoreManager manager = IgnoreManager.getInstance();
        Set<String> ignoredPlayers = manager.getIgnoredPlayers();
        
        if (ignoredPlayers.isEmpty()) {
            context.getSource().sendFeedback(Text.literal("Список ігнорованих гравців вже порожній")
                .formatted(Formatting.YELLOW));
        } else {
            int count = ignoredPlayers.size();
            manager.clearIgnoredPlayers();
            context.getSource().sendFeedback(Text.literal("Очищено список ігнорованих гравців (" + count + " гравців)")
                .formatted(Formatting.GREEN));
        }
        
        return 1;
    }
    
    private static int showHelp(CommandContext<FabricClientCommandSource> context) {
        context.getSource().sendFeedback(Text.literal("=== Команди ігнорування ===").formatted(Formatting.GOLD));
        context.getSource().sendFeedback(Text.literal("/ignore <гравець>").formatted(Formatting.YELLOW)
            .append(Text.literal(" - Додати/видалити гравця зі списку ігнорування")));
        context.getSource().sendFeedback(Text.literal("/ignore list").formatted(Formatting.YELLOW)
            .append(Text.literal(" - Показати список ігнорованих гравців")));
        context.getSource().sendFeedback(Text.literal("/ignore clear").formatted(Formatting.YELLOW)
            .append(Text.literal(" - Очистити список ігнорованих гравців")));
        
        return 1;
    }
}

# Ignore Mod для Minecraft 1.21.8

Клієнтська модифікація для Minecraft, яка дозволяє приховувати повідомлення від певних гравців за допомогою команди `/ignore`.

## Функціональність

- **Команда `/ignore <ім'я_гравця>`** - додає або видаляє гравця зі списку ігнорування
- **Команда `/ignore list`** - показує список всіх ігнорованих гравців
- **Команда `/ignore clear`** - очищає весь список ігнорованих гравців
- **Автоматичне збереження** - список ігнорованих гравців зберігається у файл і завантажується при кожному запуску гри
- **Приховування повідомлень** - всі повідомлення від ігнорованих гравців (чат, приватні повідомлення, повідомлення про смерть, приєднання/вихід) будуть приховані

## Встановлення

1. Переконайтеся, що у вас встановлений Fabric Loader для Minecraft 1.21.8
2. Встановіть Fabric API
3. Скомпілюйте мод за допомогою `./gradlew build`
4. Скопіюйте файл `.jar` з папки `build/libs/` до папки `mods` у вашому Minecraft

## Компіляція

```bash
./gradlew build
```

## Використання

### Основні команди:

- `/ignore m_th3h4mm3r` - додати гравця "m_th3h4mm3r" до списку ігнорування
- `/ignore m_th3h4mm3r` (повторно) - видалити гравця зі списку ігнорування
- `/ignore list` - показати всіх ігнорованих гравців
- `/ignore clear` - очистити список ігнорованих гравців
- `/ignore` - показати довідку по командах

### Що приховується:

- Звичайні повідомлення чату: `<PlayerName> Hello!`
- Приватні повідомлення: `PlayerName шепоче вам: secret message`
- Повідомлення про смерть: `PlayerName був убитий зомбі`
- Повідомлення про приєднання/вихід: `PlayerName приєднався до гри`

## Збереження даних

Список ігнорованих гравців зберігається у файлі:
```
.minecraft/config/ignore-mod/ignored_players.json
```

Цей файл автоматично створюється при першому використанні команди `/ignore` і завантажується при кожному запуску гри.

## Технічні деталі

- **Платформа**: Fabric для Minecraft 1.21.8
- **Тип**: Клієнтська модифікація
- **Залежності**: Fabric API, Fabric Loader
- **Java версія**: 21+

## Структура проекту

```
src/main/java/com/example/ignoremod/
├── IgnoreMod.java          # Основний клас модифікації
├── IgnoreModClient.java    # Клієнтська ініціалізація
├── IgnoreManager.java      # Управління списком ігнорованих гравців
├── IgnoreCommand.java      # Реалізація команди /ignore
└── mixin/
    └── ChatMessageMixin.java # Mixin для перехоплення чат повідомлень
```

## Ліцензія

CC0-1.0 - Публічне надбання

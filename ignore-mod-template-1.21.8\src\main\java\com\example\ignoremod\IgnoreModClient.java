package com.example.ignoremod;

import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.command.v2.ClientCommandRegistrationCallback;

public class IgnoreModClient implements ClientModInitializer {
    
    @Override
    public void onInitializeClient() {
        // Реєструємо клієнтські команди
        ClientCommandRegistrationCallback.EVENT.register((dispatcher, registryAccess) -> {
            IgnoreCommand.register(dispatcher);
        });
        
        // Завантажуємо збережені дані про ігнорованих гравців
        IgnoreManager.getInstance().loadFromFile();
        
        IgnoreMod.LOGGER.info("Ignore Mod client initialized");
    }
}

package com.example.ignoremod.mixin;

import com.example.ignoremod.IgnoreManager;
import net.minecraft.client.gui.hud.ChatHud;
import net.minecraft.text.Text;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Mixin(ChatHud.class)
public class ChatMessageMixin {
    
    // Патерни для розпізнавання різних типів повідомлень
    private static final Pattern CHAT_PATTERN = Pattern.compile("^<([^>]+)>\\s*(.*)$");
    private static final Pattern WHISPER_PATTERN = Pattern.compile("^([^\\s]+)\\s+шепоче\\s+вам:\\s*(.*)$", Pattern.CASE_INSENSITIVE);
    private static final Pattern WHISPER_PATTERN_EN = Pattern.compile("^([^\\s]+)\\s+whispers\\s+to\\s+you:\\s*(.*)$", Pattern.CASE_INSENSITIVE);
    private static final Pattern DEATH_PATTERN = Pattern.compile("^([^\\s]+)\\s+(був|була|було|died|was|got).*$", Pattern.CASE_INSENSITIVE);
    private static final Pattern JOIN_LEAVE_PATTERN = Pattern.compile("^([^\\s]+)\\s+(приєднався|покинув|joined|left).*$", Pattern.CASE_INSENSITIVE);
    
    @Inject(method = "addMessage(Lnet/minecraft/text/Text;)V", at = @At("HEAD"), cancellable = true)
    private void onChatMessage(Text message, CallbackInfo ci) {
        if (message == null) {
            return;
        }
        
        String messageText = message.getString();
        if (messageText == null || messageText.trim().isEmpty()) {
            return;
        }
        
        String playerName = extractPlayerName(messageText);
        if (playerName != null && IgnoreManager.getInstance().isPlayerIgnored(playerName)) {
            // Скасовуємо відображення повідомлення
            ci.cancel();
        }
    }
    
    /**
     * Витягує ім'я гравця з повідомлення чату
     */
    private String extractPlayerName(String message) {
        // Спробуємо різні патерни для розпізнавання імені гравця
        
        // Звичайне повідомлення чату: <PlayerName> message
        Matcher chatMatcher = CHAT_PATTERN.matcher(message);
        if (chatMatcher.matches()) {
            return chatMatcher.group(1);
        }
        
        // Приватне повідомлення (шепіт): PlayerName шепоче вам: message
        Matcher whisperMatcher = WHISPER_PATTERN.matcher(message);
        if (whisperMatcher.matches()) {
            return whisperMatcher.group(1);
        }
        
        // Приватне повідомлення (англійською): PlayerName whispers to you: message
        Matcher whisperMatcherEn = WHISPER_PATTERN_EN.matcher(message);
        if (whisperMatcherEn.matches()) {
            return whisperMatcherEn.group(1);
        }
        
        // Повідомлення про смерть: PlayerName був убитий...
        Matcher deathMatcher = DEATH_PATTERN.matcher(message);
        if (deathMatcher.matches()) {
            return deathMatcher.group(1);
        }
        
        // Повідомлення про приєднання/вихід: PlayerName приєднався до гри
        Matcher joinLeaveMatcher = JOIN_LEAVE_PATTERN.matcher(message);
        if (joinLeaveMatcher.matches()) {
            return joinLeaveMatcher.group(1);
        }
        
        // Якщо не вдалося розпізнати патерн, спробуємо витягти перше слово
        // (може бути корисно для деяких серверів з кастомними форматами)
        String[] words = message.trim().split("\\s+");
        if (words.length > 0) {
            String firstWord = words[0];
            // Перевіряємо чи це схоже на ім'я гравця (без спеціальних символів на початку)
            if (firstWord.matches("^[a-zA-Z0-9_]+$")) {
                return firstWord;
            }
        }
        
        return null;
    }
}

package com.example.ignoremod.mixin;

import com.example.ignoremod.IgnoreManager;
import net.minecraft.client.gui.hud.ChatHud;
import net.minecraft.text.Text;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.ModifyVariable;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Mixin(ChatHud.class)
public class ChatMessageMixin {
    
    // Патерни для розпізнавання різних типів повідомлень
    private static final Pattern CHAT_PATTERN = Pattern.compile("^<([^>]+)>\\s*(.*)$");
    private static final Pattern WHISPER_PATTERN = Pattern.compile("^([^\\s]+)\\s+шепоче\\s+вам:\\s*(.*)$", Pattern.CASE_INSENSITIVE);
    private static final Pattern WHISPER_PATTERN_EN = Pattern.compile("^([^\\s]+)\\s+whispers\\s+to\\s+you:\\s*(.*)$", Pattern.CASE_INSENSITIVE);
    private static final Pattern DEATH_PATTERN = Pattern.compile("^([^\\s]+)\\s+(був|була|було|died|was|got).*$", Pattern.CASE_INSENSITIVE);
    private static final Pattern JOIN_LEAVE_PATTERN = Pattern.compile("^([^\\s]+)\\s+(приєднався|покинув|joined|left).*$", Pattern.CASE_INSENSITIVE);
    
    @Inject(method = "addMessage(Lnet/minecraft/text/Text;)V", at = @At("HEAD"), cancellable = true)
    private void onChatMessage(Text message, CallbackInfo ci) {
        if (shouldCancelMessage(message)) {
            ci.cancel();
        }
    }

    // Використовуємо ModifyVariable для перехоплення всіх повідомлень
    @ModifyVariable(method = "addMessage*", at = @At("HEAD"), argsOnly = true)
    private Text modifyMessage(Text message) {
        if (shouldCancelMessage(message)) {
            // Повертаємо null або порожнє повідомлення для скасування
            return Text.empty();
        }
        return message;
    }

    // Спільний метод для перевірки чи потрібно скасувати повідомлення
    private boolean shouldCancelMessage(Text message) {
        if (message == null) {
            return false;
        }

        String messageText = message.getString();
        if (messageText == null || messageText.trim().isEmpty()) {
            return false;
        }

        // Додаємо детальне логування
        com.example.ignoremod.IgnoreMod.LOGGER.info("Processing chat message: '{}'", messageText);

        String playerName = extractPlayerName(messageText);
        com.example.ignoremod.IgnoreMod.LOGGER.info("Extracted player name: '{}'", playerName);

        if (playerName != null) {
            boolean isIgnored = IgnoreManager.getInstance().isPlayerIgnored(playerName);
            com.example.ignoremod.IgnoreMod.LOGGER.info("Player '{}' is ignored: {}", playerName, isIgnored);

            if (isIgnored) {
                com.example.ignoremod.IgnoreMod.LOGGER.info("Cancelling message from ignored player: '{}'", playerName);
                return true;
            }
        }

        return false;
    }

    
    /**
     * Витягує ім'я гравця з повідомлення чату
     */
    private String extractPlayerName(String message) {
        // Спробуємо різні патерни для розпізнавання імені гравця
        com.example.ignoremod.IgnoreMod.LOGGER.info("Trying to extract player name from: '{}'", message);

        // Звичайне повідомлення чату: <PlayerName> message
        Matcher chatMatcher = CHAT_PATTERN.matcher(message);
        if (chatMatcher.matches()) {
            String playerName = chatMatcher.group(1);
            com.example.ignoremod.IgnoreMod.LOGGER.info("Chat pattern matched, player name: '{}'", playerName);
            return playerName;
        }
        
        // Приватне повідомлення (шепіт): PlayerName шепоче вам: message
        Matcher whisperMatcher = WHISPER_PATTERN.matcher(message);
        if (whisperMatcher.matches()) {
            return whisperMatcher.group(1);
        }
        
        // Приватне повідомлення (англійською): PlayerName whispers to you: message
        Matcher whisperMatcherEn = WHISPER_PATTERN_EN.matcher(message);
        if (whisperMatcherEn.matches()) {
            return whisperMatcherEn.group(1);
        }
        
        // Повідомлення про смерть: PlayerName був убитий...
        Matcher deathMatcher = DEATH_PATTERN.matcher(message);
        if (deathMatcher.matches()) {
            return deathMatcher.group(1);
        }
        
        // Повідомлення про приєднання/вихід: PlayerName приєднався до гри
        Matcher joinLeaveMatcher = JOIN_LEAVE_PATTERN.matcher(message);
        if (joinLeaveMatcher.matches()) {
            return joinLeaveMatcher.group(1);
        }
        
        // Якщо не вдалося розпізнати патерн, спробуємо витягти перше слово
        // (може бути корисно для деяких серверів з кастомними форматами)
        String[] words = message.trim().split("\\s+");
        if (words.length > 0) {
            String firstWord = words[0];
            // Перевіряємо чи це схоже на ім'я гравця (без спеціальних символів на початку)
            if (firstWord.matches("^[a-zA-Z0-9_]+$")) {
                com.example.ignoremod.IgnoreMod.LOGGER.info("Fallback pattern matched, player name: '{}'", firstWord);
                return firstWord;
            }
        }

        com.example.ignoremod.IgnoreMod.LOGGER.info("No player name found in message: '{}'", message);
        return null;
    }
}

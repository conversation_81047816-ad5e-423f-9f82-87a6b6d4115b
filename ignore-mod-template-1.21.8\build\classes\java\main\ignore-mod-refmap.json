{"mappings": {"com/example/ignoremod/mixin/ChatMessageMixin": {"Lnet/minecraft/text/Text;getString()Ljava/lang/String;": "Lnet/minecraft/class_2561;getString()Ljava/lang/String;", "addMessage(Lnet/minecraft/text/Text;)V": "Lnet/minecraft/class_338;method_1812(Lnet/minecraft/class_2561;)V", "addMessage(Lnet/minecraft/text/Text;Lnet/minecraft/network/message/MessageSignatureData;I)V": "Lnet/minecraft/class_338;addMessage(Lnet/minecraft/class_2561;Lnet/minecraft/class_7469;I)V", "addMessage(Lnet/minecraft/text/Text;Lnet/minecraft/network/message/MessageSignatureData;ILnet/minecraft/client/gui/hud/MessageIndicator;Z)V": "Lnet/minecraft/class_338;addMessage(Lnet/minecraft/class_2561;Lnet/minecraft/class_7469;ILnet/minecraft/class_7591;Z)V", "addMessage*": "Lnet/minecraft/class_338;method_1812(Lnet/minecraft/class_2561;)V"}}, "data": {"named:intermediary": {"com/example/ignoremod/mixin/ChatMessageMixin": {"Lnet/minecraft/text/Text;getString()Ljava/lang/String;": "Lnet/minecraft/class_2561;getString()Ljava/lang/String;", "addMessage(Lnet/minecraft/text/Text;)V": "Lnet/minecraft/class_338;method_1812(Lnet/minecraft/class_2561;)V", "addMessage(Lnet/minecraft/text/Text;Lnet/minecraft/network/message/MessageSignatureData;I)V": "Lnet/minecraft/class_338;addMessage(Lnet/minecraft/class_2561;Lnet/minecraft/class_7469;I)V", "addMessage(Lnet/minecraft/text/Text;Lnet/minecraft/network/message/MessageSignatureData;ILnet/minecraft/client/gui/hud/MessageIndicator;Z)V": "Lnet/minecraft/class_338;addMessage(Lnet/minecraft/class_2561;Lnet/minecraft/class_7469;ILnet/minecraft/class_7591;Z)V", "addMessage*": "Lnet/minecraft/class_338;method_1812(Lnet/minecraft/class_2561;)V"}}}}
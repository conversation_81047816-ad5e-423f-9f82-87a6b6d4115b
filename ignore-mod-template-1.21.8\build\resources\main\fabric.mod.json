{"schemaVersion": 1, "id": "ignore-mod", "version": "1.0.0", "name": "Ignore Mod", "description": "A client-side mod that allows you to ignore messages from specific players using the /ignore command.", "authors": ["You"], "contact": {"homepage": "https://fabricmc.net/", "sources": "https://github.com/FabricMC/fabric-example-mod"}, "license": "CC0-1.0", "icon": "assets/ignore-mod/icon.png", "environment": "client", "entrypoints": {"main": ["com.example.ignoremod.IgnoreMod"], "client": ["com.example.ignoremod.IgnoreModClient"]}, "mixins": ["ignore-mod.mixins.json"], "depends": {"fabricloader": ">=0.17.2", "minecraft": "~1.21.8", "java": ">=21", "fabric-api": "*"}, "suggests": {"another-mod": "*"}}